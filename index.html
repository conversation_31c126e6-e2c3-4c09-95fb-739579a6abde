<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原型项目展示</title>
    <!-- 使用中国可访问的Tailwind CSS CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/tailwindcss/2.2.19/tailwind.min.js"></script>
    <link href="https://cdn.bootcdn.net/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <style>
        .project-item {
            transition: all 0.2s ease;
        }
        .project-item:hover {
            background-color: #f9fafb;
        }
        .loading {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        /* 自定义滚动条 */
        .overflow-y-auto::-webkit-scrollbar {
            width: 6px;
        }
        .overflow-y-auto::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .overflow-y-auto::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">

    <!-- 主要内容区域 -->
    <main class="h-screen flex flex-col">

        <!-- 左右分栏布局 -->
        <div class="flex-1 flex overflow-hidden">
            <!-- 左侧项目列表 -->
            <div class="w-80 bg-white border-r flex flex-col">
                <div class="p-4 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">项目列表</h2>
                </div>

                <!-- 加载状态 -->
                <div id="loadingState" class="flex-1 flex items-center justify-center">
                    <div class="text-center">
                        <div class="loading bg-gray-200 rounded-lg h-4 w-32 mx-auto mb-4"></div>
                        <p class="text-gray-600 text-sm">正在加载项目列表...</p>
                    </div>
                </div>

                <!-- 项目列表 -->
                <div id="projectList" class="flex-1 overflow-y-auto hidden">
                    <!-- 项目列表项将通过JavaScript动态生成 -->
                </div>

                <!-- 空状态 -->
                <div id="emptyState" class="flex-1 flex items-center justify-center hidden">
                    <div class="text-center p-6">
                        <div class="text-gray-400 mb-4">
                            <svg class="mx-auto h-16 w-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无项目</h3>
                        <p class="text-gray-600 mb-4 text-sm">在 project 文件夹中添加您的原型项目</p>
                        <button onclick="location.reload()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm transition-colors">
                            重新加载
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧项目预览区域 -->
            <div class="flex-1 flex flex-col bg-gray-50">

                <!-- 项目预览iframe -->
                <div class="flex-1 relative">
                    <iframe id="projectFrame" class="w-full h-full border-0" src="" style="display: none;"></iframe>

                    <!-- 默认欢迎页面 -->
                    <div id="welcomeScreen" class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <div class="text-gray-400 mb-6">
                                <svg class="mx-auto h-24 w-24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                                          d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
                                </svg>
                            </div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">欢迎使用原型项目展示系统</h2>
                            <p class="text-gray-600 mb-6">从左侧选择一个项目开始预览</p>
                            <div class="text-sm text-gray-500">
                                <p>💡 提示：点击左侧项目列表中的任意项目即可在此处预览</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>



    <script src="js/app.js"></script>
</body>
</html>
