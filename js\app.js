class ProjectShowcase {
    constructor() {
        this.projects = [];
        this.currentProject = null;
        this.sidebarCollapsed = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadProjects();
    }

    bindEvents() {
        // 侧边栏收起按钮
        const toggleSidebar = document.getElementById('toggleSidebar');
        if (toggleSidebar) {
            toggleSidebar.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // 刷新按钮 (如果存在)
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadProjects();
            });
        }

        // 搜索功能 (如果存在)
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterProjects(e.target.value);
            });
        }

        // 排序功能 (如果存在)
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortProjects(e.target.value);
            });
        }

        // 新标签页打开按钮
        const openInNewTab = document.getElementById('openInNewTab');
        if (openInNewTab) {
            openInNewTab.addEventListener('click', () => {
                if (this.currentProject) {
                    window.open(`project/${this.currentProject.folder}/index.html`, '_blank');
                }
            });
        }

        // 刷新预览按钮
        const refreshPreview = document.getElementById('refreshPreview');
        if (refreshPreview) {
            refreshPreview.addEventListener('click', () => {
                if (this.currentProject) {
                    this.loadProjectPreview(this.currentProject);
                }
            });
        }
    }

    async loadProjects() {
        this.showLoading();

        try {
            // 直接从配置文件读取项目列表
            const projects = await this.loadProjectsFromConfig();
            this.projects = projects;
            this.updateProjectCount();
            this.renderProjects();

        } catch (error) {
            console.error('加载项目失败:', error);
            this.showEmptyState();
        }
    }

    async loadProjectsFromConfig() {
        try {
            const configResponse = await fetch('project-config.json');
            if (configResponse.ok) {
                const config = await configResponse.json();
                // 转换日期字符串为Date对象
                return config.projects.map(project => ({
                    ...project,
                    lastModified: new Date(project.lastModified)
                }));
            }
        } catch (error) {
            console.log('读取项目配置文件失败:', error);
        }

        // 如果配置文件不存在或读取失败，返回默认项目
        return [
            {
                name: 'Todo应用',
                folder: 'todo-app',
                description: '一个简单的待办事项管理应用，支持添加、删除、标记完成任务',
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImEiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM2MzY2ZjEiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM4YjVjZjYiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+VG9kbyDlupTnlKg8L3RleHQ+PC9zdmc+',
                lastModified: new Date(),
                hasIndex: true,
                tags: ['生产力', '任务管理']
            },
            {
                name: '天气应用',
                folder: 'weather-app',
                description: '实时天气查询应用，支持多城市天气查询和历史记录',
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImIiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM3NGI5ZmYiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwOTg0ZTMiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2IpIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5aSp5rCU5bqU55SoPC90ZXh0Pjwvc3ZnPg==',
                lastModified: new Date(),
                hasIndex: true,
                tags: ['天气', '工具']
            },
            {
                name: '计算器',
                folder: 'calculator',
                description: '功能完整的计算器应用，支持基础运算和历史记录',
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImMiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM2NjdlZWEiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM3NjRiYTIiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2MpIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+6K6h566X5ZmoPC90ZXh0Pjwvc3ZnPg==',
                lastModified: new Date(),
                hasIndex: true,
                tags: ['计算', '工具']
            }
        ];
    }

    renderProjects() {
        const list = document.getElementById('projectList');
        const loading = document.getElementById('loadingState');
        const empty = document.getElementById('emptyState');

        loading.classList.add('hidden');

        if (this.projects.length === 0) {
            list.classList.add('hidden');
            empty.classList.remove('hidden');
            return;
        }

        empty.classList.add('hidden');
        list.classList.remove('hidden');

        list.innerHTML = this.projects.map(project => this.createProjectListItem(project)).join('');

        // 添加淡入动画
        setTimeout(() => {
            list.querySelectorAll('.project-item').forEach((item, index) => {
                setTimeout(() => {
                    item.classList.add('fade-in');
                }, index * 50);
            });
        }, 50);
    }

    createProjectListItem(project) {
        const isActive = this.currentProject && this.currentProject.folder === project.folder;
        return `
            <div class="project-item border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ${isActive ? 'bg-blue-50 border-blue-200' : ''}"
                 onclick="projectShowcase.selectProject('${project.folder}')">
                <!-- 收起状态下的图标 -->
                <div class="project-icon">
                    <img src="${project.thumbnail}" alt="${project.name}"
                         class="w-8 h-8 rounded object-cover"
                         title="${project.name}">
                </div>

                <!-- 展开状态下的完整内容 -->
                <div class="sidebar-content p-4">
                    <div class="flex items-start space-x-3">
                        <img src="${project.thumbnail}" alt="${project.name}"
                             class="w-12 h-12 rounded object-cover flex-shrink-0">
                        <div class="flex-1 min-w-0">
                            <h3 class="text-sm font-semibold text-gray-900 truncate ${isActive ? 'text-blue-900' : ''}">${project.name}</h3>
                            <p class="text-xs text-gray-600 mt-1 line-clamp-2">${project.description}</p>
                            <div class="flex items-center justify-between mt-2">
                                <span class="text-xs text-gray-500">${project.folder}</span>
                                <span class="text-xs text-gray-400">${this.formatDate(project.lastModified)}</span>
                            </div>
                            ${project.tags ? `
                                <div class="flex flex-wrap gap-1 mt-2">
                                    ${project.tags.map(tag => `<span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">${tag}</span>`).join('')}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.getElementById('toggleSidebar');

        this.sidebarCollapsed = !this.sidebarCollapsed;

        if (this.sidebarCollapsed) {
            sidebar.classList.add('collapsed');
            toggleBtn.classList.add('rotated');
        } else {
            sidebar.classList.remove('collapsed');
            toggleBtn.classList.remove('rotated');
        }
    }

    selectProject(folder) {
        const project = this.projects.find(p => p.folder === folder);
        if (project) {
            this.currentProject = project;
            this.loadProjectPreview(project);
            this.renderProjects(); // 重新渲染以更新选中状态
        }
    }

    loadProjectPreview(project) {
        const frame = document.getElementById('projectFrame');
        const header = document.getElementById('previewHeader');
        const title = document.getElementById('previewTitle');
        const description = document.getElementById('previewDescription');
        const welcomeScreen = document.getElementById('welcomeScreen');

        // 显示预览头部 (如果存在)
        if (header) {
            header.classList.remove('hidden');
        }
        if (title) {
            title.textContent = project.name;
        }
        if (description) {
            description.textContent = project.description;
        }

        // 隐藏欢迎屏幕，显示iframe
        if (welcomeScreen) {
            welcomeScreen.style.display = 'none';
        }
        if (frame) {
            frame.style.display = 'block';
            frame.src = `project/${project.folder}/index.html`;
        }
    }

    filterProjects(searchTerm) {
        const filtered = this.projects.filter(project =>
            project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            project.folder.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (project.tags && project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
        );

        const tempProjects = this.projects;
        this.projects = filtered;
        this.renderProjects();
        this.updateProjectCount();
        this.projects = tempProjects;
    }

    sortProjects(sortBy) {
        this.projects.sort((a, b) => {
            if (sortBy === 'name') {
                return a.name.localeCompare(b.name);
            } else if (sortBy === 'date') {
                return b.lastModified - a.lastModified;
            }
            return 0;
        });
        this.renderProjects();
    }



    updateProjectCount() {
        const projectCount = document.getElementById('projectCount');
        if (projectCount) {
            projectCount.textContent = this.projects.length;
        }
    }



    formatDate(date) {
        return date.toLocaleDateString('zh-CN');
    }

    showLoading() {
        document.getElementById('loadingState').classList.remove('hidden');
        document.getElementById('projectList').classList.add('hidden');
        document.getElementById('emptyState').classList.add('hidden');
    }

    showEmptyState() {
        document.getElementById('loadingState').classList.add('hidden');
        document.getElementById('projectList').classList.add('hidden');
        document.getElementById('emptyState').classList.remove('hidden');
    }
}

// 初始化应用
const projectShowcase = new ProjectShowcase();
