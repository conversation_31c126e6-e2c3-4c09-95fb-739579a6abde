<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例项目1 - 产品原型</title>
    <script src="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">示例项目1 - 产品原型</h1>
        
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">项目说明</h2>
            <p class="text-gray-600 mb-4">
                这是一个示例原型项目，展示了如何组织原型文件。
                您可以将您的原型项目文件夹放在 project 目录下。
            </p>
            
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-blue-100 p-4 rounded">
                    <h3 class="font-semibold text-blue-800">功能特点</h3>
                    <ul class="text-sm text-blue-600 mt-2">
                        <li>• 响应式设计</li>
                        <li>• 交互原型</li>
                        <li>• 用户流程</li>
                    </ul>
                </div>
                <div class="bg-green-100 p-4 rounded">
                    <h3 class="font-semibold text-green-800">技术栈</h3>
                    <ul class="text-sm text-green-600 mt-2">
                        <li>• HTML5</li>
                        <li>• CSS3</li>
                        <li>• JavaScript</li>
                    </ul>
                </div>
            </div>
            
            <div class="text-center">
                <button class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 transition-colors">
                    查看详情
                </button>
            </div>
        </div>
    </div>
</body>
</html>